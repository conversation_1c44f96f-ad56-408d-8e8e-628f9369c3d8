# =================================
# Dependencies
# =================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# =================================
# Environment Variables
# =================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# =================================
# Logs
# =================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# =================================
# Runtime data
# =================================
pids/
*.pid
*.seed
*.pid.lock

# =================================
# Coverage directory used by tools like istanbul
# =================================
coverage/
*.lcov
.nyc_output/

# =================================
# Dependency directories
# =================================
node_modules/
jspm_packages/

# =================================
# Optional npm cache directory
# =================================
.npm

# =================================
# Optional REPL history
# =================================
.node_repl_history

# =================================
# Output of 'npm pack'
# =================================
*.tgz

# =================================
# Yarn Integrity file
# =================================
.yarn-integrity

# =================================
# dotenv environment variables file
# =================================
.env
.env.test

# =================================
# parcel-bundler cache (https://parceljs.org/)
# =================================
.cache
.parcel-cache

# =================================
# Next.js build output
# =================================
.next

# =================================
# Nuxt.js build / generate output
# =================================
.nuxt
dist

# =================================
# Gatsby files
# =================================
.cache/
public

# =================================
# Storybook build outputs
# =================================
.out
.storybook-out

# =================================
# Temporary folders
# =================================
tmp/
temp/

# =================================
# Editor directories and files
# =================================
.vscode/
.idea/
*.swp
*.swo
*~

# =================================
# OS generated files
# =================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# =================================
# Database files
# =================================
*.sqlite
*.sqlite3
*.db

# =================================
# Build outputs
# =================================
build/
dist/
out/

# =================================
# Security and sensitive files
# =================================
*.pem
*.key
*.crt
*.p12
*.pfx
config/secrets.json

# =================================
# Backup files
# =================================
*.backup
*.bak
*.old

# =================================
# Test files
# =================================
test-results/
coverage/
# =================================
# إعدادات قاعدة البيانات MySQL
# =================================
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=your_mysql_password_here
MYSQL_DATABASE=inventorymanagement

# =================================
# مفتاح Google Gemini API
# =================================
# احصل على مفتاح API من: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# =================================
# إعدادات الخادم
# =================================
PORT=3003
NODE_ENV=development

# =================================
# إعدادات الأمان (اختيارية)
# =================================
# مفتاح سري لتشفير الجلسات
SESSION_SECRET=your_session_secret_here

# =================================
# إعدادات إضافية (اختيارية)
# =================================
# مستوى السجلات (debug, info, warn, error)
LOG_LEVEL=info

# حد أقصى لحجم الطلبات (بالبايت)
MAX_REQUEST_SIZE=10mb

# مهلة زمنية للاستعلامات (بالثواني)
QUERY_TIMEOUT=30

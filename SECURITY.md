# 🔒 سياسة الأمان - Security Policy

## 🛡️ الإصدارات المدعومة

نحن ندعم الإصدارات التالية بتحديثات الأمان:

| الإصدار | مدعوم          |
| ------- | ------------- |
| 1.0.x   | ✅ مدعوم      |
| < 1.0   | ❌ غير مدعوم  |

## 🚨 الإبلاغ عن الثغرات الأمنية

إذا اكتشفت ثغرة أمنية، يرجى عدم الإبلاغ عنها علناً. بدلاً من ذلك:

### 📧 التواصل المباشر
- **البريد الإلكتروني**: <EMAIL>
- **الموضوع**: `[SECURITY] وكيل البيانات الذكي - ثغرة أمنية`

### 📝 معلومات مطلوبة
يرجى تضمين المعلومات التالية:
- وصف مفصل للثغرة
- خطوات إعادة الإنتاج
- التأثير المحتمل
- أي حلول مقترحة

### ⏱️ زمن الاستجابة
- **الاستجابة الأولية**: خلال 48 ساعة
- **تقييم الثغرة**: خلال 7 أيام
- **إصلاح الثغرة**: حسب الخطورة (1-30 يوم)

## 🔐 أفضل الممارسات الأمنية

### للمطورين
- ✅ استخدم متغيرات البيئة للمعلومات الحساسة
- ✅ لا تضع مفاتيح API في الكود
- ✅ استخدم HTTPS في الإنتاج
- ✅ قم بتحديث التبعيات بانتظام
- ✅ راجع الكود قبل النشر

### للمستخدمين
- ✅ احم ملف `.env` من الوصول العام
- ✅ استخدم كلمات مرور قوية لقاعدة البيانات
- ✅ قم بتحديث التطبيق بانتظام
- ✅ راقب سجلات النظام
- ✅ استخدم جدار حماية

## 🛠️ إعدادات الأمان الموصى بها

### متغيرات البيئة
```env
# استخدم كلمة مرور قوية
MYSQL_PASSWORD=strong_password_here

# استخدم مفتاح جلسة عشوائي
SESSION_SECRET=random_secret_key_here

# قيد CORS للنطاقات المسموحة
CORS_ORIGIN=https://yourdomain.com
```

### قاعدة البيانات
- إنشاء مستخدم منفصل للتطبيق
- منح صلاحيات محدودة فقط
- تشفير الاتصالات
- نسخ احتياطية منتظمة

### الخادم
- استخدام HTTPS
- تحديث نظام التشغيل
- مراقبة الموارد
- سجلات الأمان

## 🔍 فحص الأمان

### أدوات الفحص المستخدمة
- `npm audit` - فحص التبعيات
- `eslint-plugin-security` - فحص الكود
- `helmet` - حماية Express.js

### فحص دوري
```bash
# فحص التبعيات
npm audit

# إصلاح الثغرات التلقائية
npm audit fix

# فحص الكود
npm run security-check
```

## 📋 قائمة التحقق الأمنية

### قبل النشر
- [ ] فحص جميع التبعيات
- [ ] إزالة المعلومات الحساسة من الكود
- [ ] تفعيل HTTPS
- [ ] إعداد متغيرات البيئة
- [ ] اختبار الصلاحيات
- [ ] مراجعة سجلات الأمان

### بعد النشر
- [ ] مراقبة السجلات
- [ ] فحص الأداء
- [ ] تحديث التبعيات
- [ ] نسخ احتياطية
- [ ] اختبار الاختراق

## 🚫 ما لا يجب فعله

- ❌ لا تضع مفاتيح API في الكود
- ❌ لا تستخدم كلمات مرور ضعيفة
- ❌ لا تتجاهل تحديثات الأمان
- ❌ لا تشارك ملف `.env`
- ❌ لا تستخدم HTTP في الإنتاج

## 📞 جهات الاتصال

### فريق الأمان
- **المطور الرئيسي**: أحمد الدعيس
- **البريد الإلكتروني**: <EMAIL>
- **الاستجابة**: 24/7 للثغرات الحرجة

### الموارد الإضافية
- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Node.js Security Checklist](https://blog.risingstack.com/node-js-security-checklist/)
- [Express.js Security Best Practices](https://expressjs.com/en/advanced/best-practice-security.html)

---

**تذكر**: الأمان مسؤولية مشتركة بين المطورين والمستخدمين. 🤝
